/* Admin interface styles following existing design patterns */

/* Admin Login Styles */
.admin-login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  font-family: 'Open Sans', sans-serif;
}

.admin-login-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  padding: 40px;
  width: 100%;
  max-width: 400px;
  text-align: center;
}

.admin-login-header h1 {
  color: #1b8ae4;
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.admin-login-header p {
  color: #666;
  font-size: 14px;
  margin: 0 0 32px 0;
  line-height: 1.4;
}

.admin-login-form {
  text-align: left;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  color: #333;
  font-weight: 500;
  margin-bottom: 6px;
  font-size: 14px;
}

.form-group input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #1b8ae4;
  box-shadow: 0 0 0 3px rgba(27, 138, 228, 0.1);
}

.form-group input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.login-button {
  width: 100%;
  background-color: #1b8ae4;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.login-button:hover:not(:disabled) {
  background-color: #1570c7;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(27, 138, 228, 0.3);
}

.login-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  background-color: #fee;
  color: #c33;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #fcc;
  margin-bottom: 20px;
  font-size: 14px;
  text-align: center;
}

.admin-login-footer {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.admin-login-footer p {
  color: #999;
  font-size: 12px;
  margin: 0;
}

/* Customer Onboarding Form Styles */
.onboarding-container {
  min-height: 100vh;
  background: #f8fafc;
  padding: 20px;
  font-family: 'Open Sans', sans-serif;
}

.onboarding-header {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.onboarding-header h1 {
  color: #1b8ae4;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.logout-button {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.logout-button:hover {
  background: #c82333;
}

.onboarding-form-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 32px;
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 32px;
}

.form-section:last-child {
  margin-bottom: 0;
}

.form-section-title {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e1e5e9;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-row.single {
  grid-template-columns: 1fr;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  box-sizing: border-box;
  transition: border-color 0.2s ease;
}

.form-group textarea:focus {
  outline: none;
  border-color: #1b8ae4;
  box-shadow: 0 0 0 3px rgba(27, 138, 228, 0.1);
}

.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  box-sizing: border-box;
  transition: border-color 0.2s ease;
}

.form-group select:focus {
  outline: none;
  border-color: #1b8ae4;
  box-shadow: 0 0 0 3px rgba(27, 138, 228, 0.1);
}

.submit-button {
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 24px;
}

.submit-button:hover:not(:disabled) {
  background-color: #218838;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.submit-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 16px 20px;
  border-radius: 8px;
  border: 1px solid #c3e6cb;
  margin-bottom: 20px;
  font-size: 14px;
}

.field-error {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-login-card {
    padding: 24px;
    margin: 20px;
  }
  
  .onboarding-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .onboarding-form-card {
    padding: 20px;
    margin: 0 10px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
