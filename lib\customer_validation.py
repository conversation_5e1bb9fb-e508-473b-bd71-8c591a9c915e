"""
Customer data validation module based on Firestore schema.
Provides comprehensive validation and sanitization for customer onboarding data.
"""

import re
import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import html

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Custom exception for validation errors"""
    def __init__(self, message: str, field: str = None, errors: Dict[str, str] = None):
        self.message = message
        self.field = field
        self.errors = errors or {}
        super().__init__(message)

def sanitize_string(value: Any) -> str:
    """
    Sanitize string input to prevent injection attacks.
    
    Args:
        value: Input value to sanitize
        
    Returns:
        Sanitized string
    """
    if value is None:
        return ""
    
    # Convert to string and strip whitespace
    sanitized = str(value).strip()
    
    # HTML escape to prevent XSS
    sanitized = html.escape(sanitized)
    
    return sanitized

def validate_email(email: str) -> bool:
    """
    Validate email format.
    
    Args:
        email: Email address to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone: str) -> bool:
    """
    Validate phone number format (Australian format).
    
    Args:
        phone: Phone number to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not phone:
        return False
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Australian phone numbers: 10 digits (mobile) or 8-10 digits (landline)
    return len(digits_only) >= 8 and len(digits_only) <= 10

def validate_abn(abn: str) -> bool:
    """
    Validate Australian Business Number (ABN).
    
    Args:
        abn: ABN to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not abn:
        return False
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', abn)
    
    # ABN must be exactly 11 digits
    if len(digits_only) != 11:
        return False
    
    # Basic ABN checksum validation
    weights = [10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19]
    
    try:
        # Subtract 1 from the first digit
        first_digit = int(digits_only[0]) - 1
        if first_digit < 0:
            return False
        
        # Calculate weighted sum
        total = first_digit * weights[0]
        for i in range(1, 11):
            total += int(digits_only[i]) * weights[i]
        
        # Check if divisible by 89
        return total % 89 == 0
    except (ValueError, IndexError):
        return False

def validate_postcode(postcode: str) -> bool:
    """
    Validate Australian postcode.
    
    Args:
        postcode: Postcode to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not postcode:
        return False
    
    # Australian postcodes are 4 digits
    return re.match(r'^\d{4}$', postcode) is not None

def validate_url(url: str) -> bool:
    """
    Validate URL format.
    
    Args:
        url: URL to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not url:
        return False
    
    pattern = r'^https?://[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(/.*)?$'
    return re.match(pattern, url) is not None

def validate_business_hours(hours: Dict[str, Any]) -> Dict[str, str]:
    """
    Validate business hours structure.
    
    Args:
        hours: Business hours dictionary
        
    Returns:
        Dictionary of validation errors (empty if valid)
    """
    errors = {}
    
    if not isinstance(hours, dict):
        errors['business_hours'] = 'Business hours must be an object'
        return errors
    
    required_days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    time_pattern = r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$'
    
    for day in required_days:
        if day not in hours:
            errors[f'business_hours.{day}'] = f'{day.capitalize()} hours are required'
            continue
        
        day_hours = hours[day]
        if not isinstance(day_hours, dict):
            errors[f'business_hours.{day}'] = f'{day.capitalize()} hours must be an object'
            continue
        
        if 'open' not in day_hours or 'close' not in day_hours:
            errors[f'business_hours.{day}'] = f'{day.capitalize()} must have open and close times'
            continue
        
        open_time = day_hours.get('open', '')
        close_time = day_hours.get('close', '')
        
        if open_time and not re.match(time_pattern, open_time):
            errors[f'business_hours.{day}.open'] = 'Invalid time format (use HH:MM)'
        
        if close_time and not re.match(time_pattern, close_time):
            errors[f'business_hours.{day}.close'] = 'Invalid time format (use HH:MM)'
    
    # Validate timezone
    if 'timezone' not in hours:
        errors['business_hours.timezone'] = 'Timezone is required'
    elif not isinstance(hours['timezone'], str) or not hours['timezone'].strip():
        errors['business_hours.timezone'] = 'Valid timezone is required'
    
    return errors

def validate_service_areas(service_areas: Dict[str, Any]) -> Dict[str, str]:
    """
    Validate service areas structure.
    
    Args:
        service_areas: Service areas dictionary
        
    Returns:
        Dictionary of validation errors (empty if valid)
    """
    errors = {}
    
    if not isinstance(service_areas, dict):
        errors['service_areas'] = 'Service areas must be an object'
        return errors
    
    # Validate suburbs
    if 'suburbs' in service_areas:
        suburbs = service_areas['suburbs']
        if not isinstance(suburbs, list):
            errors['service_areas.suburbs'] = 'Suburbs must be an array'
        elif not all(isinstance(suburb, str) and suburb.strip() for suburb in suburbs):
            errors['service_areas.suburbs'] = 'All suburbs must be non-empty strings'
    
    # Validate postcodes
    if 'postcodes' in service_areas:
        postcodes = service_areas['postcodes']
        if not isinstance(postcodes, list):
            errors['service_areas.postcodes'] = 'Postcodes must be an array'
        elif not all(validate_postcode(str(pc)) for pc in postcodes):
            errors['service_areas.postcodes'] = 'All postcodes must be valid 4-digit Australian postcodes'
    
    # Validate radius
    if 'radius_km' in service_areas:
        radius = service_areas['radius_km']
        if not isinstance(radius, (int, float)) or radius <= 0:
            errors['service_areas.radius_km'] = 'Radius must be a positive number'
    
    return errors

def validate_address(address: Dict[str, Any]) -> Dict[str, str]:
    """
    Validate address structure.
    
    Args:
        address: Address dictionary
        
    Returns:
        Dictionary of validation errors (empty if valid)
    """
    errors = {}
    
    if not isinstance(address, dict):
        errors['address'] = 'Address must be an object'
        return errors
    
    required_fields = ['street', 'suburb', 'state', 'postcode']
    
    for field in required_fields:
        if field not in address or not address[field]:
            errors[f'address.{field}'] = f'{field.capitalize()} is required'
        elif field == 'postcode' and not validate_postcode(str(address[field])):
            errors[f'address.{field}'] = 'Invalid postcode format'
    
    return errors

def validate_customer_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate complete customer data against the Firestore schema.

    Args:
        data: Customer data to validate

    Returns:
        Dictionary containing 'valid' boolean and 'errors' dict

    Raises:
        ValidationError: If validation fails
    """
    errors = {}

    if not isinstance(data, dict):
        raise ValidationError("Request data must be a JSON object")

    # Validate basic_info section
    basic_info = data.get('basic_info', {})
    if not isinstance(basic_info, dict):
        errors['basic_info'] = 'Basic info must be an object'
    else:
        # Required fields
        required_basic = ['company_name', 'business_abn', 'website_url', 'user_type', 'status']

        for field in required_basic:
            value = basic_info.get(field)
            if not value or not str(value).strip():
                errors[f'basic_info.{field}'] = f'{field.replace("_", " ").title()} is required'

        # Validate specific formats
        if 'business_abn' in basic_info and basic_info['business_abn']:
            if not validate_abn(basic_info['business_abn']):
                errors['basic_info.business_abn'] = 'Invalid ABN format'

        if 'website_url' in basic_info and basic_info['website_url']:
            if not validate_url(basic_info['website_url']):
                errors['basic_info.website_url'] = 'Invalid URL format'

    # Validate contact_info section
    contact_info = data.get('contact_info', {})
    if not isinstance(contact_info, dict):
        errors['contact_info'] = 'Contact info must be an object'
    else:
        # Validate primary email
        primary_email = contact_info.get('primary_email')
        if not primary_email:
            errors['contact_info.primary_email'] = 'Primary email is required'
        elif not validate_email(primary_email):
            errors['contact_info.primary_email'] = 'Invalid email format'

        # Validate secondary emails
        secondary_emails = contact_info.get('secondary_emails', [])
        if secondary_emails and not isinstance(secondary_emails, list):
            errors['contact_info.secondary_emails'] = 'Secondary emails must be an array'
        elif secondary_emails:
            for i, email in enumerate(secondary_emails):
                if not validate_email(email):
                    errors[f'contact_info.secondary_emails[{i}]'] = 'Invalid email format'

        # Validate phone numbers
        phone = contact_info.get('phone')
        if phone and not validate_phone(phone):
            errors['contact_info.phone'] = 'Invalid phone number format'

        mobile = contact_info.get('mobile')
        if mobile and not validate_phone(mobile):
            errors['contact_info.mobile'] = 'Invalid mobile number format'

        # Validate address
        address = contact_info.get('address', {})
        address_errors = validate_address(address)
        errors.update(address_errors)

    # Validate tradie_details section
    tradie_details = data.get('tradie_details', {})
    if not isinstance(tradie_details, dict):
        errors['tradie_details'] = 'Tradie details must be an object'
    else:
        # Validate tradie_type
        if not tradie_details.get('tradie_type'):
            errors['tradie_details.tradie_type'] = 'Tradie type is required'

        # Validate specializations
        specializations = tradie_details.get('specializations', [])
        if not isinstance(specializations, list):
            errors['tradie_details.specializations'] = 'Specializations must be an array'
        elif not specializations:
            errors['tradie_details.specializations'] = 'At least one specialization is required'

        # Validate service areas
        service_areas = tradie_details.get('service_areas', {})
        service_area_errors = validate_service_areas(service_areas)
        errors.update(service_area_errors)

        # Validate business hours
        business_hours = tradie_details.get('business_hours', {})
        business_hours_errors = validate_business_hours(business_hours)
        errors.update(business_hours_errors)

    # Validate AI settings section
    ai_settings = data.get('ai_settings', {})
    if not isinstance(ai_settings, dict):
        errors['ai_settings'] = 'AI settings must be an object'
    else:
        # Validate max_quote_amount
        max_quote = ai_settings.get('max_quote_amount')
        if max_quote is not None:
            if not isinstance(max_quote, (int, float)) or max_quote <= 0:
                errors['ai_settings.max_quote_amount'] = 'Max quote amount must be a positive number'

    return {
        'valid': len(errors) == 0,
        'errors': errors
    }

def sanitize_customer_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sanitize customer data to prevent injection attacks.

    Args:
        data: Raw customer data

    Returns:
        Sanitized customer data
    """
    if not isinstance(data, dict):
        return {}

    sanitized = {}

    # Sanitize basic_info
    if 'basic_info' in data and isinstance(data['basic_info'], dict):
        sanitized['basic_info'] = {
            'company_name': sanitize_string(data['basic_info'].get('company_name')),
            'business_abn': sanitize_string(data['basic_info'].get('business_abn')),
            'website_url': sanitize_string(data['basic_info'].get('website_url')),
            'user_type': sanitize_string(data['basic_info'].get('user_type')),
            'status': sanitize_string(data['basic_info'].get('status')),
            'api_key': sanitize_string(data['basic_info'].get('api_key', ''))
        }

    # Sanitize contact_info
    if 'contact_info' in data and isinstance(data['contact_info'], dict):
        contact = data['contact_info']
        sanitized['contact_info'] = {
            'primary_email': sanitize_string(contact.get('primary_email')),
            'secondary_emails': [sanitize_string(email) for email in contact.get('secondary_emails', [])],
            'phone': sanitize_string(contact.get('phone')),
            'mobile': sanitize_string(contact.get('mobile')),
            'address': {
                'street': sanitize_string(contact.get('address', {}).get('street')),
                'suburb': sanitize_string(contact.get('address', {}).get('suburb')),
                'state': sanitize_string(contact.get('address', {}).get('state')),
                'postcode': sanitize_string(contact.get('address', {}).get('postcode'))
            }
        }

    # Copy other sections with basic sanitization
    for section in ['tradie_details', 'ai_settings', 'subscription', 'metrics']:
        if section in data:
            sanitized[section] = data[section]  # Deep sanitization would be added here

    return sanitized
